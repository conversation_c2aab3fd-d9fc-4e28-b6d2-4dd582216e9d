# Radio Browser MCP Integration

This project integrates the Radio Browser API into a Mastra MCP (Model Context Protocol) server, allowing AI agents to discover and interact with radio stations from around the world.

## Features

### Radio Browser Tools

The integration provides the following tools:

1. **getRadioStatsTool** - Get statistics about the radio browser database
2. **searchStationsByNameTool** - Search for radio stations by name
3. **getStationsByCountryTool** - Get radio stations from a specific country
4. **searchStationsByTagTool** - Search for radio stations by genre/tag
5. **getPopularStationsTool** - Get the most popular radio stations
6. **getRecentStationsTool** - Get recently added radio stations
7. **getStationByUuidTool** - Get detailed information about a specific station
8. **getCountriesTool** - Get list of available countries
9. **getTagsTool** - Get list of available genres/tags

### Radio Agent

The `radioAgent` is an AI agent that can:
- Help users discover radio stations
- Search by name, country, or genre
- Provide station details and streaming URLs
- Suggest popular and recent stations
- List available countries and genres

## Usage

### Starting the MCP Server

```bash
npm run dev
```

### Example Interactions

You can interact with the radio agent through the MCP interface:

**Search for stations:**
- "Find BBC radio stations"
- "Show me rock music stations from Germany"
- "What are the most popular radio stations?"

**Explore by location:**
- "List radio stations from France"
- "Show me stations from the US"

**Browse by genre:**
- "Find jazz radio stations"
- "Show me classical music stations"
- "What news radio stations are available?"

**Get information:**
- "What countries have radio stations?"
- "What music genres are available?"
- "Show me radio browser statistics"

## API Integration

The integration uses the Radio Browser API (https://www.radio-browser.info/), which provides:

- Over 30,000 radio stations worldwide
- Real-time station information
- Multiple server endpoints for reliability
- Free and open API access

### Server Selection

The implementation automatically:
- Discovers available Radio Browser API servers via DNS
- Implements load balancing across multiple servers
- Provides fallback servers for reliability
- Handles server failures gracefully

## Technical Details

### File Structure

```
src/mastra/
├── tools/
│   └── radio-browser-tool.ts    # Radio Browser API tools
├── agents/
│   └── radio-agent.ts           # Radio discovery agent
└── index.ts                     # Main Mastra configuration
```

### Dependencies

The integration uses:
- `@mastra/core` - Core MCP functionality
- `zod` - Schema validation
- Node.js built-in `dns` module for server discovery
- Standard `fetch` API for HTTP requests

### Error Handling

- Automatic server failover
- Graceful handling of API errors
- Proper error messages for users
- Fallback to known servers if DNS lookup fails

## Testing

Run the test script to verify the integration:

```bash
npx tsx test-radio.ts
```

This will test:
- Radio browser statistics
- Station search functionality
- Country-based filtering
- Popular stations retrieval
- Tag/genre listing

## Station Information

Each radio station includes:
- **Name** - Station name
- **URL** - Direct stream URL
- **Country** - Country of origin
- **Language** - Broadcasting language
- **Tags** - Genre/category tags
- **Bitrate** - Audio quality
- **Codec** - Audio format
- **Votes** - User rating
- **Homepage** - Station website
- **Geographic coordinates** - Location data

## Supported Features

### Search Capabilities
- Text search in station names
- Country-based filtering (2-letter codes)
- Genre/tag-based filtering
- Popular stations (by votes)
- Recently added stations

### Data Access
- Real-time station information
- Streaming URLs
- Station metadata
- Geographic information
- Quality metrics

## Example Code

```typescript
import { radioAgent } from './src/mastra/agents/radio-agent';

// The agent can be used in MCP conversations
// Users can ask questions like:
// "Find rock stations from the UK"
// "What are the most popular jazz stations?"
// "Show me recent additions to the database"
```

## Contributing

To extend the radio browser integration:

1. Add new tools in `radio-browser-tool.ts`
2. Update the agent instructions in `radio-agent.ts`
3. Test new functionality with the test script
4. Update documentation

## License

This integration follows the same license as the main project.
