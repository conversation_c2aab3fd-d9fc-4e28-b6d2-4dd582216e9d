
import { <PERSON><PERSON> } from '@mastra/core/mastra';
import { <PERSON><PERSON>Logger } from '@mastra/loggers';
import { LibSQLStore } from '@mastra/libsql';

import { weatherAgent } from './agents/weather-agent';
import { radioAgent } from './agents/radio-agent';

export const mastra = new Mastra({
  agents: { weatherAgent, radioAgent },
  storage: new LibSQLStore({
    // stores telemetry, evals, ... into memory storage, if it needs to persist, change to file:../mastra.db
    url: ":memory:",
  }),
  logger: new PinoLogger({
    name: '<PERSON><PERSON>',
    level: 'info',
  }),
});

