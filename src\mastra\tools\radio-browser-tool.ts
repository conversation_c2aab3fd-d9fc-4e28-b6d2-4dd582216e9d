import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import * as dns from 'dns';
import { promisify } from 'util';

// Interfaces for API responses
interface RadioStation {
  stationuuid: string;
  name: string;
  url: string;
  url_resolved: string;
  homepage: string;
  favicon: string;
  tags: string;
  country: string;
  countrycode: string;
  state: string;
  language: string;
  languagecodes: string;
  votes: number;
  lastchangetime: string;
  lastchangetime_iso8601: string;
  codec: string;
  bitrate: number;
  hls: number;
  lastcheckok: number;
  lastchecktime: string;
  lastchecktime_iso8601: string;
  lastcheckoktime: string;
  lastcheckoktime_iso8601: string;
  lastlocalchecktime: string;
  lastlocalchecktime_iso8601: string;
  clicktimestamp: string;
  clicktimestamp_iso8601: string;
  clickcount: number;
  clicktrend: number;
  ssl_error: number;
  geo_lat: number;
  geo_long: number;
  has_extended_info: boolean;
}

interface RadioBrowserStats {
  supported_version: number;
  software_version: string;
  status: string;
  stations: number;
  stations_broken: number;
  tags: number;
  clicks_last_hour: number;
  clicks_last_day: number;
  languages: number;
  countries: number;
}

// DNS lookup promisified
const lookup = promisify(dns.lookup);

class RadioBrowserAPI {
  private baseUrls: string[] = [];

  async getRadioBrowserBaseUrls(): Promise<string[]> {
    if (this.baseUrls.length > 0) {
      return this.baseUrls;
    }

    try {
      // Get all IPs for the radio-browser API
      const addresses = await lookup('all.api.radio-browser.info', { all: true });
      const hosts: string[] = [];

      for (const addr of addresses) {
        try {
          // Do reverse lookup to get hostname
          const hostnames = await promisify(dns.reverse)(addr.address);
          if (hostnames.length > 0 && !hosts.includes(hostnames[0])) {
            hosts.push(hostnames[0]);
          }
        } catch (error) {
          // If reverse lookup fails, use the IP directly
          if (!hosts.includes(addr.address)) {
            hosts.push(addr.address);
          }
        }
      }

      // Sort and add https prefix
      hosts.sort();
      this.baseUrls = hosts.map(host => `https://${host}`);
      return this.baseUrls;
    } catch (error) {
      // Fallback to known servers
      this.baseUrls = [
        'https://de1.api.radio-browser.info',
        'https://nl1.api.radio-browser.info',
        'https://at1.api.radio-browser.info'
      ];
      return this.baseUrls;
    }
  }

  async downloadFromRadioBrowser(path: string, params?: Record<string, any>): Promise<any> {
    const servers = await this.getRadioBrowserBaseUrls();

    // Shuffle servers for load balancing
    const shuffledServers = [...servers].sort(() => Math.random() - 0.5);

    for (let i = 0; i < shuffledServers.length; i++) {
      const serverBase = shuffledServers[i];
      const url = `${serverBase}${path}`;

      try {
        console.log(`Trying server: ${serverBase} (attempt ${i + 1})`);

        const requestOptions: RequestInit = {
          method: params ? 'POST' : 'GET',
          headers: {
            'User-Agent': 'RadioMCP/1.0.0',
            'Content-Type': 'application/json',
          },
        };

        if (params) {
          requestOptions.body = JSON.stringify(params);
        }

        const response = await fetch(url, requestOptions);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      } catch (error) {
        console.error(`Failed to download from ${url}:`, error);
        if (i === shuffledServers.length - 1) {
          throw new Error(`All radio-browser servers failed. Last error: ${error}`);
        }
      }
    }

    throw new Error('No radio-browser servers available');
  }
}

const radioBrowserAPI = new RadioBrowserAPI();

// Tool for getting radio browser statistics
export const getRadioStatsTool = createTool({
  id: 'get-radio-stats',
  description: 'Get statistics about the radio browser database',
  inputSchema: z.object({}),
  outputSchema: z.object({
    supported_version: z.number(),
    software_version: z.string(),
    status: z.string(),
    stations: z.number(),
    stations_broken: z.number(),
    tags: z.number(),
    clicks_last_hour: z.number(),
    clicks_last_day: z.number(),
    languages: z.number(),
    countries: z.number(),
  }),
  execute: async () => {
    const stats = await radioBrowserAPI.downloadFromRadioBrowser('/json/stats');
    return stats as RadioBrowserStats;
  },
});

// Tool for searching stations by name
export const searchStationsByNameTool = createTool({
  id: 'search-stations-by-name',
  description: 'Search for radio stations by name',
  inputSchema: z.object({
    name: z.string().describe('Name of the radio station to search for'),
    limit: z.number().optional().default(10).describe('Maximum number of results to return'),
  }),
  outputSchema: z.array(z.object({
    stationuuid: z.string(),
    name: z.string(),
    url: z.string(),
    url_resolved: z.string(),
    homepage: z.string(),
    favicon: z.string(),
    tags: z.string(),
    country: z.string(),
    countrycode: z.string(),
    state: z.string(),
    language: z.string(),
    languagecodes: z.string(),
    votes: z.number(),
    codec: z.string(),
    bitrate: z.number(),
    lastcheckok: z.number(),
    clickcount: z.number(),
    geo_lat: z.number(),
    geo_long: z.number(),
  })),
  execute: async ({ context }) => {
    const params = {
      name: context.name,
      limit: context.limit,
    };

    const stations = await radioBrowserAPI.downloadFromRadioBrowser('/json/stations/search', params);
    return stations as RadioStation[];
  },
});

// Tool for getting stations by country
export const getStationsByCountryTool = createTool({
  id: 'get-stations-by-country',
  description: 'Get radio stations from a specific country',
  inputSchema: z.object({
    countryCode: z.string().length(2).describe('Two-letter country code (e.g., US, DE, FR)'),
    limit: z.number().optional().default(20).describe('Maximum number of results to return'),
  }),
  outputSchema: z.array(z.object({
    stationuuid: z.string(),
    name: z.string(),
    url: z.string(),
    url_resolved: z.string(),
    homepage: z.string(),
    favicon: z.string(),
    tags: z.string(),
    country: z.string(),
    countrycode: z.string(),
    state: z.string(),
    language: z.string(),
    languagecodes: z.string(),
    votes: z.number(),
    codec: z.string(),
    bitrate: z.number(),
    lastcheckok: z.number(),
    clickcount: z.number(),
    geo_lat: z.number(),
    geo_long: z.number(),
  })),
  execute: async ({ context }) => {
    const path = `/json/stations/bycountrycodeexact/${context.countryCode.toUpperCase()}`;
    const stations = await radioBrowserAPI.downloadFromRadioBrowser(path);

    // Apply limit
    const limitedStations = stations.slice(0, context.limit);
    return limitedStations as RadioStation[];
  },
});

// Tool for searching stations by tag
export const searchStationsByTagTool = createTool({
  id: 'search-stations-by-tag',
  description: 'Search for radio stations by tag/genre (e.g., rock, jazz, news, classical)',
  inputSchema: z.object({
    tag: z.string().describe('Tag/genre to search for'),
    limit: z.number().optional().default(15).describe('Maximum number of results to return'),
  }),
  outputSchema: z.array(z.object({
    stationuuid: z.string(),
    name: z.string(),
    url: z.string(),
    url_resolved: z.string(),
    homepage: z.string(),
    favicon: z.string(),
    tags: z.string(),
    country: z.string(),
    countrycode: z.string(),
    state: z.string(),
    language: z.string(),
    languagecodes: z.string(),
    votes: z.number(),
    codec: z.string(),
    bitrate: z.number(),
    lastcheckok: z.number(),
    clickcount: z.number(),
    geo_lat: z.number(),
    geo_long: z.number(),
  })),
  execute: async ({ context }) => {
    const params = {
      tag: context.tag,
      limit: context.limit,
    };

    const stations = await radioBrowserAPI.downloadFromRadioBrowser('/json/stations/search', params);
    return stations as RadioStation[];
  },
});

// Tool for getting popular stations
export const getPopularStationsTool = createTool({
  id: 'get-popular-stations',
  description: 'Get the most popular radio stations based on votes and clicks',
  inputSchema: z.object({
    limit: z.number().optional().default(20).describe('Maximum number of results to return'),
  }),
  outputSchema: z.array(z.object({
    stationuuid: z.string(),
    name: z.string(),
    url: z.string(),
    url_resolved: z.string(),
    homepage: z.string(),
    favicon: z.string(),
    tags: z.string(),
    country: z.string(),
    countrycode: z.string(),
    state: z.string(),
    language: z.string(),
    languagecodes: z.string(),
    votes: z.number(),
    codec: z.string(),
    bitrate: z.number(),
    lastcheckok: z.number(),
    clickcount: z.number(),
    geo_lat: z.number(),
    geo_long: z.number(),
  })),
  execute: async ({ context }) => {
    const path = `/json/stations/topvote/${context.limit}`;
    const stations = await radioBrowserAPI.downloadFromRadioBrowser(path);
    return stations as RadioStation[];
  },
});

// Tool for getting recently added stations
export const getRecentStationsTool = createTool({
  id: 'get-recent-stations',
  description: 'Get recently added radio stations',
  inputSchema: z.object({
    limit: z.number().optional().default(15).describe('Maximum number of results to return'),
  }),
  outputSchema: z.array(z.object({
    stationuuid: z.string(),
    name: z.string(),
    url: z.string(),
    url_resolved: z.string(),
    homepage: z.string(),
    favicon: z.string(),
    tags: z.string(),
    country: z.string(),
    countrycode: z.string(),
    state: z.string(),
    language: z.string(),
    languagecodes: z.string(),
    votes: z.number(),
    codec: z.string(),
    bitrate: z.number(),
    lastcheckok: z.number(),
    clickcount: z.number(),
    geo_lat: z.number(),
    geo_long: z.number(),
  })),
  execute: async ({ context }) => {
    const path = `/json/stations/lastchange/${context.limit}`;
    const stations = await radioBrowserAPI.downloadFromRadioBrowser(path);
    return stations as RadioStation[];
  },
});

// Tool for getting station details by UUID
export const getStationByUuidTool = createTool({
  id: 'get-station-by-uuid',
  description: 'Get detailed information about a specific radio station by its UUID',
  inputSchema: z.object({
    uuid: z.string().describe('Station UUID'),
  }),
  outputSchema: z.object({
    stationuuid: z.string(),
    name: z.string(),
    url: z.string(),
    url_resolved: z.string(),
    homepage: z.string(),
    favicon: z.string(),
    tags: z.string(),
    country: z.string(),
    countrycode: z.string(),
    state: z.string(),
    language: z.string(),
    languagecodes: z.string(),
    votes: z.number(),
    codec: z.string(),
    bitrate: z.number(),
    lastcheckok: z.number(),
    clickcount: z.number(),
    geo_lat: z.number(),
    geo_long: z.number(),
  }),
  execute: async ({ context }) => {
    const path = `/json/stations/byuuid/${context.uuid}`;
    const stations = await radioBrowserAPI.downloadFromRadioBrowser(path);

    if (!stations || stations.length === 0) {
      throw new Error(`Station with UUID ${context.uuid} not found`);
    }

    return stations[0] as RadioStation;
  },
});

// Tool for getting available countries
export const getCountriesTool = createTool({
  id: 'get-countries',
  description: 'Get list of available countries with radio stations',
  inputSchema: z.object({
    limit: z.number().optional().default(50).describe('Maximum number of countries to return'),
  }),
  outputSchema: z.array(z.object({
    name: z.string(),
    iso_3166_1: z.string(),
    stationcount: z.number(),
  })),
  execute: async ({ context }) => {
    const countries = await radioBrowserAPI.downloadFromRadioBrowser('/json/countries');

    // Sort by station count and apply limit
    const sortedCountries = countries
      .sort((a: any, b: any) => b.stationcount - a.stationcount)
      .slice(0, context.limit);

    return sortedCountries;
  },
});

// Tool for getting available tags/genres
export const getTagsTool = createTool({
  id: 'get-tags',
  description: 'Get list of available tags/genres for radio stations',
  inputSchema: z.object({
    limit: z.number().optional().default(30).describe('Maximum number of tags to return'),
  }),
  outputSchema: z.array(z.object({
    name: z.string(),
    stationcount: z.number(),
  })),
  execute: async ({ context }) => {
    const tags = await radioBrowserAPI.downloadFromRadioBrowser('/json/tags');

    // Sort by station count and apply limit
    const sortedTags = tags
      .sort((a: any, b: any) => b.stationcount - a.stationcount)
      .slice(0, context.limit);

    return sortedTags;
  },
});
